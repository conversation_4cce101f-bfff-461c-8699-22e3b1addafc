#!/usr/bin/env node
"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const commander_1 = require("commander");
const chalk_1 = __importDefault(require("chalk"));
const inquirer_1 = __importDefault(require("inquirer"));
const ora_1 = __importDefault(require("ora"));
const core_1 = require("@/agent/core");
const manager_1 = require("@/session/manager");
const config_1 = require("@/config");
const logger_1 = require("@/utils/logger");
const program = new commander_1.Command();
program
    .name('agentic')
    .description('AI-powered CLI tool with agentic capabilities')
    .version('1.0.0');
// Global options
program
    .option('-v, --verbose', 'Enable verbose logging')
    .option('-d, --debug', 'Enable debug logging')
    .option('--provider <provider>', 'LLM provider (deepseek|ollama)', 'deepseek')
    .option('--model <model>', 'Model to use')
    .option('--session <sessionId>', 'Session ID to use')
    .option('--temperature <temp>', 'Temperature for LLM (0.0-1.0)', parseFloat)
    .option('--max-tokens <tokens>', 'Maximum tokens for LLM response', parseInt);
// Chat command - main interactive mode
program
    .command('chat')
    .description('Start interactive chat with the AI agent')
    .option('-m, --message <message>', 'Send a single message instead of interactive mode')
    .action(async (chatOptions, cmd) => {
    const globalOpts = cmd.parent?.opts();
    await runChatCommand(chatOptions, globalOpts);
});
// Session management commands
const sessionCmd = program
    .command('session')
    .description('Manage sessions');
sessionCmd
    .command('list')
    .description('List all sessions')
    .action(async (_options, cmd) => {
    const globalOpts = cmd.parent?.parent?.opts();
    await listSessions(globalOpts);
});
sessionCmd
    .command('create')
    .description('Create a new session')
    .option('-n, --name <name>', 'Session name')
    .option('-d, --directory <dir>', 'Working directory')
    .action(async (options, cmd) => {
    const globalOpts = cmd.parent?.parent?.opts();
    await createSession(options, globalOpts);
});
sessionCmd
    .command('delete <sessionId>')
    .description('Delete a session')
    .action(async (sessionId, cmd) => {
    const globalOpts = cmd.parent?.parent?.opts();
    await deleteSession(sessionId, globalOpts);
});
sessionCmd
    .command('export <sessionId> <outputPath>')
    .description('Export a session to file')
    .action(async (sessionId, outputPath, cmd) => {
    const globalOpts = cmd.parent?.parent?.opts();
    await exportSession(sessionId, outputPath, globalOpts);
});
sessionCmd
    .command('import <importPath>')
    .description('Import a session from file')
    .action(async (importPath, cmd) => {
    const globalOpts = cmd.parent?.parent?.opts();
    await importSession(importPath, globalOpts);
});
// Configuration commands
const configCmd = program
    .command('config')
    .description('Manage configuration');
configCmd
    .command('show')
    .description('Show current configuration')
    .action(async () => {
    console.log(JSON.stringify(config_1.config.getConfig(), null, 2));
});
configCmd
    .command('set-api-key <provider> <apiKey>')
    .description('Set API key for a provider')
    .action(async (provider, apiKey) => {
    if (provider === 'deepseek') {
        config_1.config.setProviderApiKey('deepseek', apiKey);
        console.log(chalk_1.default.green('✓ Deepseek API key set successfully'));
    }
    else {
        console.log(chalk_1.default.red('✗ Unsupported provider. Use: deepseek'));
    }
});
configCmd
    .command('reset')
    .description('Reset configuration to defaults')
    .action(async () => {
    const { confirm } = await inquirer_1.default.prompt([
        {
            type: 'confirm',
            name: 'confirm',
            message: 'Are you sure you want to reset configuration to defaults?',
            default: false,
        },
    ]);
    if (confirm) {
        config_1.config.resetConfig();
        console.log(chalk_1.default.green('✓ Configuration reset to defaults'));
    }
});
// Main chat command implementation
async function runChatCommand(options, globalOpts) {
    try {
        setupLogging(globalOpts);
        const agentConfig = await buildAgentConfig(globalOpts);
        const agent = new core_1.Agent(agentConfig, process.cwd());
        // Initialize agent with session
        await agent.initialize(globalOpts.session);
        console.log(chalk_1.default.blue('🤖 Agentic CLI Agent initialized'));
        console.log(chalk_1.default.gray(`Provider: ${agentConfig.provider} | Model: ${agentConfig.model}`));
        console.log(chalk_1.default.gray(`Session: ${agent.getSessionId()}`));
        console.log(chalk_1.default.gray(`Working Directory: ${agent.getContext().workingDirectory}`));
        console.log();
        if (options.message) {
            // Single message mode
            await processSingleMessage(agent, options.message);
        }
        else {
            // Interactive mode
            await runInteractiveChat(agent);
        }
        await agent.cleanup();
    }
    catch (error) {
        console.error(chalk_1.default.red('✗ Error:'), error.message);
        if (globalOpts.debug) {
            console.error(error.stack);
        }
        process.exit(1);
    }
}
async function processSingleMessage(agent, message) {
    const spinner = (0, ora_1.default)('Processing message...').start();
    try {
        const response = await agent.sendMessage(message);
        spinner.stop();
        console.log(chalk_1.default.green('🤖 Agent:'));
        console.log(response);
    }
    catch (error) {
        spinner.fail('Failed to process message');
        throw error;
    }
}
async function runInteractiveChat(agent) {
    console.log(chalk_1.default.yellow('💬 Interactive chat mode. Type "exit" to quit, "help" for commands.'));
    console.log();
    while (true) {
        try {
            const { message } = await inquirer_1.default.prompt([
                {
                    type: 'input',
                    name: 'message',
                    message: chalk_1.default.blue('You:'),
                    validate: (input) => input.trim().length > 0 || 'Please enter a message',
                },
            ]);
            if (message.toLowerCase() === 'exit') {
                console.log(chalk_1.default.yellow('👋 Goodbye!'));
                break;
            }
            if (message.toLowerCase() === 'help') {
                showChatHelp();
                continue;
            }
            if (message.toLowerCase().startsWith('/')) {
                await handleChatCommand(agent, message);
                continue;
            }
            const spinner = (0, ora_1.default)('Agent is thinking...').start();
            try {
                const response = await agent.sendMessage(message);
                spinner.stop();
                console.log();
                console.log(chalk_1.default.green('🤖 Agent:'));
                console.log(response);
                console.log();
            }
            catch (error) {
                spinner.fail('Failed to process message');
                console.error(chalk_1.default.red('Error:'), error.message);
                console.log();
            }
        }
        catch (error) {
            if (error.name === 'ExitPromptError') {
                console.log(chalk_1.default.yellow('\n👋 Goodbye!'));
                break;
            }
            throw error;
        }
    }
}
function showChatHelp() {
    console.log();
    console.log(chalk_1.default.yellow('Available commands:'));
    console.log(chalk_1.default.gray('  exit          - Exit the chat'));
    console.log(chalk_1.default.gray('  help          - Show this help'));
    console.log(chalk_1.default.gray('  /status       - Show agent status'));
    console.log(chalk_1.default.gray('  /context      - Show project context'));
    console.log(chalk_1.default.gray('  /refresh      - Refresh project context'));
    console.log(chalk_1.default.gray('  /cd <path>    - Change working directory'));
    console.log(chalk_1.default.gray('  /session      - Show session info'));
    console.log();
}
async function handleChatCommand(agent, command) {
    const parts = command.slice(1).split(' ');
    const cmd = parts[0]?.toLowerCase() || '';
    const args = parts.slice(1);
    try {
        switch (cmd) {
            case 'status':
                console.log(chalk_1.default.blue('Agent Status:'));
                console.log(`  Ready: ${agent.isReady()}`);
                console.log(`  Session: ${agent.getSessionId()}`);
                console.log(`  Provider: ${agent.config.provider}`);
                console.log(`  Model: ${agent.config.model}`);
                break;
            case 'context':
                const context = agent.getContext();
                console.log(chalk_1.default.blue('Project Context:'));
                console.log(`  Working Directory: ${context.workingDirectory}`);
                console.log(`  Project Type: ${context.projectContext.type}`);
                console.log(`  Files: ${context.projectContext.structure.totalFiles}`);
                console.log(`  Directories: ${context.projectContext.structure.totalDirectories}`);
                break;
            case 'refresh':
                const spinner = (0, ora_1.default)('Refreshing project context...').start();
                await agent.refreshContext();
                spinner.succeed('Project context refreshed');
                break;
            case 'cd':
                if (args.length === 0) {
                    console.log(chalk_1.default.red('Usage: /cd <path>'));
                    break;
                }
                const newPath = args.join(' ');
                const cdSpinner = (0, ora_1.default)(`Changing directory to ${newPath}...`).start();
                await agent.updateWorkingDirectory(newPath);
                cdSpinner.succeed(`Changed directory to ${newPath}`);
                break;
            case 'session':
                console.log(chalk_1.default.blue('Session Info:'));
                console.log(`  ID: ${agent.getSessionId()}`);
                console.log(`  Working Directory: ${agent.getContext().workingDirectory}`);
                break;
            default:
                console.log(chalk_1.default.red(`Unknown command: ${cmd}`));
                showChatHelp();
        }
    }
    catch (error) {
        console.error(chalk_1.default.red('Command error:'), error.message);
    }
    console.log();
}
// Session management functions
async function listSessions(globalOpts) {
    try {
        setupLogging(globalOpts);
        const sessions = await manager_1.sessionManager.listSessions();
        if (sessions.length === 0) {
            console.log(chalk_1.default.yellow('No sessions found.'));
            return;
        }
        console.log(chalk_1.default.blue(`Found ${sessions.length} sessions:`));
        console.log();
        for (const session of sessions) {
            console.log(chalk_1.default.green(`📁 ${session.name}`));
            console.log(chalk_1.default.gray(`   ID: ${session.id}`));
            console.log(chalk_1.default.gray(`   Created: ${session.createdAt.toLocaleString()}`));
            console.log(chalk_1.default.gray(`   Updated: ${session.updatedAt.toLocaleString()}`));
            console.log(chalk_1.default.gray(`   Directory: ${session.workingDirectory}`));
            console.log(chalk_1.default.gray(`   Messages: ${session.messages.length}`));
            console.log();
        }
    }
    catch (error) {
        console.error(chalk_1.default.red('✗ Error listing sessions:'), error.message);
        process.exit(1);
    }
}
async function createSession(options, globalOpts) {
    try {
        setupLogging(globalOpts);
        const spinner = (0, ora_1.default)('Creating new session...').start();
        const session = await manager_1.sessionManager.createSession(options.name, options.directory);
        spinner.succeed('Session created successfully');
        console.log(chalk_1.default.green(`✓ Session created:`));
        console.log(chalk_1.default.gray(`   ID: ${session.id}`));
        console.log(chalk_1.default.gray(`   Name: ${session.name}`));
        console.log(chalk_1.default.gray(`   Directory: ${session.workingDirectory}`));
    }
    catch (error) {
        console.error(chalk_1.default.red('✗ Error creating session:'), error.message);
        process.exit(1);
    }
}
async function deleteSession(sessionId, globalOpts) {
    try {
        setupLogging(globalOpts);
        const { confirm } = await inquirer_1.default.prompt([
            {
                type: 'confirm',
                name: 'confirm',
                message: `Are you sure you want to delete session ${sessionId}?`,
                default: false,
            },
        ]);
        if (!confirm) {
            console.log(chalk_1.default.yellow('Session deletion cancelled.'));
            return;
        }
        const spinner = (0, ora_1.default)('Deleting session...').start();
        await manager_1.sessionManager.deleteSession(sessionId);
        spinner.succeed('Session deleted successfully');
        console.log(chalk_1.default.green(`✓ Session ${sessionId} deleted`));
    }
    catch (error) {
        console.error(chalk_1.default.red('✗ Error deleting session:'), error.message);
        process.exit(1);
    }
}
async function exportSession(sessionId, outputPath, globalOpts) {
    try {
        setupLogging(globalOpts);
        const spinner = (0, ora_1.default)('Exporting session...').start();
        await manager_1.sessionManager.exportSession(sessionId, outputPath);
        spinner.succeed('Session exported successfully');
        console.log(chalk_1.default.green(`✓ Session exported to: ${outputPath}`));
    }
    catch (error) {
        console.error(chalk_1.default.red('✗ Error exporting session:'), error.message);
        process.exit(1);
    }
}
async function importSession(importPath, globalOpts) {
    try {
        setupLogging(globalOpts);
        const spinner = (0, ora_1.default)('Importing session...').start();
        const session = await manager_1.sessionManager.importSession(importPath);
        spinner.succeed('Session imported successfully');
        console.log(chalk_1.default.green(`✓ Session imported:`));
        console.log(chalk_1.default.gray(`   ID: ${session.id}`));
        console.log(chalk_1.default.gray(`   Name: ${session.name}`));
    }
    catch (error) {
        console.error(chalk_1.default.red('✗ Error importing session:'), error.message);
        process.exit(1);
    }
}
// Utility functions
function setupLogging(options) {
    if (options.debug) {
        logger_1.logger.setLogLevel(logger_1.LogLevel.DEBUG);
    }
    else if (options.verbose) {
        logger_1.logger.setLogLevel(logger_1.LogLevel.INFO);
    }
}
async function buildAgentConfig(options) {
    const cliConfig = config_1.config.getConfig();
    const provider = options.provider || cliConfig.defaultProvider;
    if (!['deepseek', 'ollama'].includes(provider)) {
        throw new Error(`Unsupported provider: ${provider}. Use: deepseek, ollama`);
    }
    const providerConfig = config_1.config.getProviderConfig(provider);
    let model = options.model;
    if (!model) {
        model = provider === 'deepseek'
            ? providerConfig.defaultModel
            : providerConfig.defaultModel;
    }
    const agentConfig = {
        provider: provider,
        model,
        ...(options.temperature !== undefined && { temperature: options.temperature }),
        ...(options.maxTokens !== undefined && { maxTokens: options.maxTokens }),
    };
    if (provider === 'deepseek') {
        const deepseekConfig = providerConfig;
        if (deepseekConfig.apiKey) {
            agentConfig.apiKey = deepseekConfig.apiKey;
        }
        if (providerConfig.baseUrl) {
            agentConfig.baseUrl = providerConfig.baseUrl;
        }
        if (!agentConfig.apiKey) {
            throw new Error('Deepseek API key not configured. Use: agentic config set-api-key deepseek <key>');
        }
    }
    else if (provider === 'ollama') {
        if (providerConfig.baseUrl) {
            agentConfig.baseUrl = providerConfig.baseUrl;
        }
    }
    return agentConfig;
}
// Error handling
process.on('unhandledRejection', (reason, promise) => {
    logger_1.logger.error('Unhandled Rejection at:', promise, 'reason:', String(reason));
    process.exit(1);
});
process.on('uncaughtException', (error) => {
    logger_1.logger.error('Uncaught Exception:', error);
    process.exit(1);
});
// Graceful shutdown
process.on('SIGINT', () => {
    console.log(chalk_1.default.yellow('\n👋 Shutting down gracefully...'));
    process.exit(0);
});
process.on('SIGTERM', () => {
    console.log(chalk_1.default.yellow('\n👋 Shutting down gracefully...'));
    process.exit(0);
});
// Parse command line arguments
program.parse();
//# sourceMappingURL=cli.js.map