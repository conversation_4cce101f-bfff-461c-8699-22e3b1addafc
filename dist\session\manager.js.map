{"version": 3, "file": "manager.js", "sourceRoot": "", "sources": ["../../src/session/manager.ts"], "names": [], "mappings": ";;;;;;AAAA,wDAA0B;AAC1B,gDAAwB;AACxB,mCAAgC;AAEhC,qCAAkC;AAClC,2CAAwC;AACxC,mDAAuD;AAEvD,MAAa,cAAc;IACjB,MAAM,CAAC,QAAQ,CAAiB;IAChC,cAAc,GAAmB,IAAI,CAAC;IACtC,WAAW,CAAS;IAE5B;QACE,IAAI,CAAC,WAAW,GAAG,eAAM,CAAC,oBAAoB,EAAE,CAAC;QACjD,eAAM,CAAC,iBAAiB,EAAE,CAAC;IAC7B,CAAC;IAEM,MAAM,CAAC,WAAW;QACvB,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,CAAC;YAC7B,cAAc,CAAC,QAAQ,GAAG,IAAI,cAAc,EAAE,CAAC;QACjD,CAAC;QACD,OAAO,cAAc,CAAC,QAAQ,CAAC;IACjC,CAAC;IAEM,KAAK,CAAC,aAAa,CACxB,IAAa,EACb,gBAAyB;QAEzB,MAAM,SAAS,GAAG,IAAA,eAAM,GAAE,CAAC;QAC3B,MAAM,WAAW,GAAG,IAAI,IAAI,WAAW,IAAI,IAAI,EAAE,CAAC,cAAc,EAAE,EAAE,CAAC;QACrE,MAAM,OAAO,GAAG,gBAAgB,IAAI,OAAO,CAAC,GAAG,EAAE,CAAC;QAElD,eAAM,CAAC,IAAI,CAAC,yBAAyB,WAAW,EAAE,EAAE,EAAE,SAAS,EAAE,OAAO,EAAE,EAAE,gBAAgB,CAAC,CAAC;QAE9F,2BAA2B;QAC3B,MAAM,cAAc,GAAG,MAAM,4BAAgB,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;QACvE,MAAM,SAAS,GAAG,4BAAgB,CAAC,eAAe,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;QAE7E,MAAM,cAAc,GAAmB;YACrC,gBAAgB,EAAE,cAAc,CAAC,SAAS;YAC1C,SAAS;YACT,YAAY,EAAE,cAAc,CAAC,YAAY;YACzC,WAAW,EAAE,OAAO,CAAC,GAA6B;YAClD,GAAG,CAAC,cAAc,CAAC,OAAO,IAAI,EAAE,OAAO,EAAE,cAAc,CAAC,OAAO,EAAE,CAAC;SACnE,CAAC;QAEF,MAAM,OAAO,GAAY;YACvB,EAAE,EAAE,SAAS;YACb,IAAI,EAAE,WAAW;YACjB,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,gBAAgB,EAAE,OAAO;YACzB,OAAO,EAAE,cAAc;YACvB,QAAQ,EAAE,EAAE;YACZ,QAAQ,EAAE;gBACR,WAAW,EAAE,cAAc,CAAC,IAAI;gBAChC,UAAU,EAAE,cAAc,CAAC,SAAS,CAAC,UAAU;gBAC/C,gBAAgB,EAAE,cAAc,CAAC,SAAS,CAAC,gBAAgB;aAC5D;SACF,CAAC;QAEF,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAChC,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC;QAE9B,eAAM,CAAC,IAAI,CAAC,8BAA8B,EAAE;YAC1C,SAAS;YACT,WAAW,EAAE,cAAc,CAAC,IAAI;YAChC,KAAK,EAAE,cAAc,CAAC,SAAS,CAAC,UAAU;SAC3C,EAAE,gBAAgB,EAAE,SAAS,CAAC,CAAC;QAEhC,OAAO,OAAO,CAAC;IACjB,CAAC;IAEM,KAAK,CAAC,WAAW,CAAC,SAAiB;QACxC,MAAM,WAAW,GAAG,cAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,SAAS,OAAO,CAAC,CAAC;QAErE,IAAI,CAAC,kBAAE,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE,CAAC;YAChC,MAAM,IAAI,KAAK,CAAC,sBAAsB,SAAS,EAAE,CAAC,CAAC;QACrD,CAAC;QAED,IAAI,CAAC;YACH,MAAM,WAAW,GAAG,MAAM,kBAAE,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;YACnD,MAAM,OAAO,GAAG,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,CAAC;YAErD,oCAAoC;YACpC,IAAI,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,EAAE,CAAC;gBACvC,MAAM,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC;YAC5C,CAAC;YAED,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC;YAE9B,eAAM,CAAC,IAAI,CAAC,6BAA6B,EAAE;gBACzC,SAAS;gBACT,YAAY,EAAE,OAAO,CAAC,QAAQ,CAAC,MAAM;aACtC,EAAE,gBAAgB,EAAE,SAAS,CAAC,CAAC;YAEhC,OAAO,OAAO,CAAC;QACjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,2BAA2B,SAAS,EAAE,EAAE,KAAK,EAAE,gBAAgB,CAAC,CAAC;YAC9E,MAAM,IAAI,KAAK,CAAC,2BAA2B,KAAK,EAAE,CAAC,CAAC;QACtD,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,WAAW,CAAC,OAAgB;QACvC,IAAI,CAAC;YACH,OAAO,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;YAC/B,MAAM,WAAW,GAAG,cAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,OAAO,CAAC,EAAE,OAAO,CAAC,CAAC;YACtE,MAAM,iBAAiB,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;YAEzD,MAAM,kBAAE,CAAC,SAAS,CAAC,WAAW,EAAE,iBAAiB,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC;YAElE,eAAM,CAAC,KAAK,CAAC,eAAe,EAAE,EAAE,SAAS,EAAE,OAAO,CAAC,EAAE,EAAE,EAAE,gBAAgB,EAAE,OAAO,CAAC,EAAE,CAAC,CAAC;QACzF,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,2BAA2B,OAAO,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,gBAAgB,EAAE,OAAO,CAAC,EAAE,CAAC,CAAC;YAC3F,MAAM,IAAI,KAAK,CAAC,2BAA2B,KAAK,EAAE,CAAC,CAAC;QACtD,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,aAAa,CAAC,SAAiB;QAC1C,MAAM,WAAW,GAAG,cAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,SAAS,OAAO,CAAC,CAAC;QAErE,IAAI,kBAAE,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE,CAAC;YAC/B,MAAM,kBAAE,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;YAE7B,IAAI,IAAI,CAAC,cAAc,EAAE,EAAE,KAAK,SAAS,EAAE,CAAC;gBAC1C,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;YAC7B,CAAC;YAED,eAAM,CAAC,IAAI,CAAC,iBAAiB,EAAE,EAAE,SAAS,EAAE,EAAE,gBAAgB,CAAC,CAAC;QAClE,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,YAAY;QACvB,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,MAAM,kBAAE,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACjD,MAAM,YAAY,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;YAElE,MAAM,QAAQ,GAAc,EAAE,CAAC;YAE/B,KAAK,MAAM,IAAI,IAAI,YAAY,EAAE,CAAC;gBAChC,IAAI,CAAC;oBACH,MAAM,WAAW,GAAG,cAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;oBACtD,MAAM,WAAW,GAAG,MAAM,kBAAE,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;oBACnD,MAAM,OAAO,GAAG,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,CAAC;oBACrD,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBACzB,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,eAAM,CAAC,IAAI,CAAC,gCAAgC,IAAI,EAAE,EAAE,KAAK,EAAE,gBAAgB,CAAC,CAAC;gBAC/E,CAAC;YACH,CAAC;YAED,2CAA2C;YAC3C,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,CAAC;YAEvE,OAAO,QAAQ,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,EAAE,gBAAgB,CAAC,CAAC;YACjE,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAEM,iBAAiB;QACtB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC7B,CAAC;IAEM,KAAK,CAAC,UAAU,CAAC,OAAqB;QAC3C,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;YACzB,MAAM,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC;QACvC,CAAC;QAED,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAE3C,wBAAwB;QACxB,MAAM,UAAU,GAAG,eAAM,CAAC,SAAS,EAAE,CAAC,OAAO,CAAC,UAAU,CAAC;QACzD,IAAI,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,MAAM,GAAG,UAAU,EAAE,CAAC;YACrD,IAAI,CAAC,cAAc,CAAC,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,UAAU,CAAC,CAAC;QACjF,CAAC;QAED,IAAI,eAAM,CAAC,SAAS,EAAE,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;YACxC,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAC9C,CAAC;QAED,eAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE;YACvC,IAAI,EAAE,OAAO,CAAC,IAAI;YAClB,SAAS,EAAE,IAAI,CAAC,cAAc,CAAC,EAAE;SAClC,EAAE,gBAAgB,EAAE,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC;IAC/C,CAAC;IAEM,WAAW;QAChB,OAAO,IAAI,CAAC,cAAc,EAAE,QAAQ,IAAI,EAAE,CAAC;IAC7C,CAAC;IAEM,KAAK,CAAC,qBAAqB,CAAC,OAAgB;QACjD,IAAI,CAAC;YACH,eAAM,CAAC,IAAI,CAAC,4BAA4B,EAAE,EAAE,SAAS,EAAE,OAAO,CAAC,EAAE,EAAE,EAAE,gBAAgB,EAAE,OAAO,CAAC,EAAE,CAAC,CAAC;YAEnG,MAAM,cAAc,GAAG,MAAM,4BAAgB,CAAC,cAAc,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;YACvF,MAAM,SAAS,GAAG,4BAAgB,CAAC,eAAe,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;YAE7E,OAAO,CAAC,OAAO,GAAG;gBAChB,gBAAgB,EAAE,cAAc,CAAC,SAAS;gBAC1C,SAAS;gBACT,YAAY,EAAE,cAAc,CAAC,YAAY;gBACzC,WAAW,EAAE,OAAO,CAAC,GAA6B;gBAClD,GAAG,CAAC,cAAc,CAAC,OAAO,IAAI,EAAE,OAAO,EAAE,cAAc,CAAC,OAAO,EAAE,CAAC;aACnE,CAAC;YAEF,OAAO,CAAC,QAAQ,GAAG;gBACjB,GAAG,OAAO,CAAC,QAAQ;gBACnB,WAAW,EAAE,cAAc,CAAC,IAAI;gBAChC,UAAU,EAAE,cAAc,CAAC,SAAS,CAAC,UAAU;gBAC/C,gBAAgB,EAAE,cAAc,CAAC,SAAS,CAAC,gBAAgB;aAC5D,CAAC;YAEF,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;YAEhC,eAAM,CAAC,IAAI,CAAC,2BAA2B,EAAE;gBACvC,SAAS,EAAE,OAAO,CAAC,EAAE;gBACrB,KAAK,EAAE,cAAc,CAAC,SAAS,CAAC,UAAU;aAC3C,EAAE,gBAAgB,EAAE,OAAO,CAAC,EAAE,CAAC,CAAC;QACnC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,EAAE,gBAAgB,EAAE,OAAO,CAAC,EAAE,CAAC,CAAC;YACvF,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEO,oBAAoB,CAAC,OAAgB;QAC3C,MAAM,MAAM,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,aAAa;QAC5C,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,OAAO,CAAC,OAAO,CAAC,gBAAgB,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;QAChF,OAAO,GAAG,GAAG,MAAM,CAAC;IACtB,CAAC;IAEO,gBAAgB,CAAC,OAAgB;QACvC,OAAO;YACL,GAAG,OAAO;YACV,SAAS,EAAE,OAAO,CAAC,SAAS,CAAC,WAAW,EAAE;YAC1C,SAAS,EAAE,OAAO,CAAC,SAAS,CAAC,WAAW,EAAE;YAC1C,OAAO,EAAE;gBACP,GAAG,OAAO,CAAC,OAAO;gBAClB,gBAAgB,EAAE;oBAChB,GAAG,OAAO,CAAC,OAAO,CAAC,gBAAgB;oBACnC,WAAW,EAAE,OAAO,CAAC,OAAO,CAAC,gBAAgB,CAAC,WAAW,CAAC,WAAW,EAAE;iBACxE;gBACD,SAAS,EAAE;oBACT,KAAK,EAAE,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;oBAC5D,WAAW,EAAE,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;oBACxE,WAAW,EAAE,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;oBACxE,MAAM,EAAE,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;oBAC9D,WAAW,EAAE,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;iBACzE;aACF;SACF,CAAC;IACJ,CAAC;IAEO,kBAAkB,CAAC,IAAS;QAClC,OAAO;YACL,GAAG,IAAI;YACP,SAAS,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC;YACnC,SAAS,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC;YACnC,OAAO,EAAE;gBACP,GAAG,IAAI,CAAC,OAAO;gBACf,gBAAgB,EAAE;oBAChB,GAAG,IAAI,CAAC,OAAO,CAAC,gBAAgB;oBAChC,WAAW,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,WAAW,CAAC;iBACjE;gBACD,SAAS,EAAE;oBACT,KAAK,EAAE,IAAI,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC;oBAC5C,WAAW,EAAE,IAAI,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,WAAW,CAAC;oBACxD,WAAW,EAAE,IAAI,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,WAAW,CAAC;oBACxD,MAAM,EAAE,IAAI,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC;oBAC9C,WAAW,EAAE,IAAI,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,WAAW,CAAC;iBACzD;aACF;SACF,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,aAAa,CAAC,SAAiB,EAAE,UAAkB;QAC9D,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;QAClD,MAAM,UAAU,GAAG;YACjB,OAAO,EAAE,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC;YACvC,UAAU,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACpC,OAAO,EAAE,OAAO;SACjB,CAAC;QAEF,MAAM,kBAAE,CAAC,SAAS,CAAC,UAAU,EAAE,UAAU,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC;QAC1D,eAAM,CAAC,IAAI,CAAC,kBAAkB,EAAE,EAAE,SAAS,EAAE,UAAU,EAAE,EAAE,gBAAgB,EAAE,SAAS,CAAC,CAAC;IAC1F,CAAC;IAEM,KAAK,CAAC,aAAa,CAAC,UAAkB;QAC3C,MAAM,UAAU,GAAG,MAAM,kBAAE,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QACjD,MAAM,OAAO,GAAG,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QAE5D,qCAAqC;QACrC,OAAO,CAAC,EAAE,GAAG,IAAA,eAAM,GAAE,CAAC;QACtB,OAAO,CAAC,IAAI,GAAG,GAAG,OAAO,CAAC,IAAI,aAAa,CAAC;QAE5C,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAChC,eAAM,CAAC,IAAI,CAAC,kBAAkB,EAAE,EAAE,SAAS,EAAE,OAAO,CAAC,EAAE,EAAE,EAAE,gBAAgB,EAAE,OAAO,CAAC,EAAE,CAAC,CAAC;QAEzF,OAAO,OAAO,CAAC;IACjB,CAAC;IAEM,KAAK,CAAC,kBAAkB,CAAC,SAAiB,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;QACvE,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC;QAC3C,MAAM,UAAU,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,CAAC;QAEjD,IAAI,YAAY,GAAG,CAAC,CAAC;QACrB,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;YAC/B,IAAI,OAAO,CAAC,SAAS,GAAG,UAAU,EAAE,CAAC;gBACnC,MAAM,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;gBACrC,YAAY,EAAE,CAAC;YACjB,CAAC;QACH,CAAC;QAED,eAAM,CAAC,IAAI,CAAC,cAAc,YAAY,eAAe,EAAE,SAAS,EAAE,gBAAgB,CAAC,CAAC;IACtF,CAAC;CACF;AApTD,wCAoTC;AAEY,QAAA,cAAc,GAAG,cAAc,CAAC,WAAW,EAAE,CAAC"}