import { <PERSON><PERSON><PERSON><PERSON>, AgentMessage, AgentConfig, Tool, Tool<PERSON>all } from '@/types';
export declare class OllamaProvider implements LLMProvider {
    readonly name = "ollama";
    private client;
    constructor();
    validateConfig(config: AgentConfig): boolean;
    sendMessage(messages: AgentMessage[], config: AgentConfig): Promise<string>;
    sendToolMessage(messages: AgentMessage[], tools: Tool[], config: AgentConfig): Promise<{
        message: string;
        toolCalls?: ToolCall[];
    }>;
    private ensureModelAvailable;
    private pullModel;
    private formatMessages;
    private createToolsPrompt;
    private parseToolCalls;
    private zodToJsonSchema;
}
//# sourceMappingURL=ollama.d.ts.map