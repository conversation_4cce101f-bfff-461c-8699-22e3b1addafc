"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.shellOperations = exports.ShellOperations = void 0;
const cross_spawn_1 = require("cross-spawn");
const events_1 = require("events");
const logger_1 = require("@/utils/logger");
const config_1 = require("@/config");
class ShellOperations extends events_1.EventEmitter {
    static instance;
    runningProcesses = new Map();
    static getInstance() {
        if (!ShellOperations.instance) {
            ShellOperations.instance = new ShellOperations();
        }
        return ShellOperations.instance;
    }
    async executeCommand(command, context, options = {}) {
        try {
            this.validateCommand(command, context);
            const { timeout = 30000, // 30 seconds default
            cwd = context.workingDirectory, env = { ...process.env, ...context.environment }, shell = true, detached = false, stdio = 'pipe', } = options;
            logger_1.logger.info(`Executing command: ${command}`, {
                cwd,
                timeout,
                detached
            }, 'ShellOperations', context.sessionId);
            const startTime = Date.now();
            return new Promise((resolve) => {
                let stdout = '';
                let stderr = '';
                let isResolved = false;
                // Parse command and arguments
                const [cmd, ...args] = this.parseCommand(command);
                const childProcess = (0, cross_spawn_1.spawn)(cmd, args, {
                    cwd,
                    env,
                    shell,
                    detached,
                    stdio,
                });
                if (childProcess.pid) {
                    this.runningProcesses.set(childProcess.pid, childProcess);
                }
                // Handle stdout
                if (childProcess.stdout) {
                    childProcess.stdout.on('data', (data) => {
                        const chunk = data.toString();
                        stdout += chunk;
                        this.emit('stdout', {
                            sessionId: context.sessionId,
                            data: chunk,
                            command,
                            pid: childProcess.pid
                        });
                    });
                }
                // Handle stderr
                if (childProcess.stderr) {
                    childProcess.stderr.on('data', (data) => {
                        const chunk = data.toString();
                        stderr += chunk;
                        this.emit('stderr', {
                            sessionId: context.sessionId,
                            data: chunk,
                            command,
                            pid: childProcess.pid
                        });
                    });
                }
                // Handle process exit
                childProcess.on('close', (exitCode) => {
                    if (isResolved)
                        return;
                    isResolved = true;
                    const duration = Date.now() - startTime;
                    if (childProcess.pid) {
                        this.runningProcesses.delete(childProcess.pid);
                    }
                    const result = {
                        success: exitCode === 0,
                        message: exitCode === 0
                            ? `Command executed successfully: ${command}`
                            : `Command failed with exit code ${exitCode}: ${command}`,
                        data: {
                            stdout: stdout.trim(),
                            stderr: stderr.trim(),
                            exitCode: exitCode || 0,
                            command,
                            workingDirectory: cwd,
                            duration,
                            pid: childProcess.pid,
                        },
                    };
                    logger_1.logger.info(`Command completed`, {
                        command,
                        exitCode,
                        duration,
                        stdoutLength: stdout.length,
                        stderrLength: stderr.length,
                    }, 'ShellOperations', context.sessionId);
                    resolve(result);
                });
                // Handle process error
                childProcess.on('error', (error) => {
                    if (isResolved)
                        return;
                    isResolved = true;
                    const duration = Date.now() - startTime;
                    if (childProcess.pid) {
                        this.runningProcesses.delete(childProcess.pid);
                    }
                    logger_1.logger.error(`Command error: ${command}`, error, 'ShellOperations', context.sessionId);
                    resolve({
                        success: false,
                        message: `Command error: ${command}`,
                        error: error.message,
                        data: {
                            stdout: stdout.trim(),
                            stderr: stderr.trim(),
                            exitCode: -1,
                            command,
                            workingDirectory: cwd,
                            duration,
                            pid: childProcess.pid,
                        },
                    });
                });
                // Set timeout if specified
                if (timeout > 0 && !detached) {
                    setTimeout(() => {
                        if (isResolved)
                            return;
                        isResolved = true;
                        logger_1.logger.warn(`Command timeout: ${command}`, { timeout }, 'ShellOperations', context.sessionId);
                        // Kill the process
                        if (childProcess.pid) {
                            this.killProcess(childProcess.pid);
                        }
                        const duration = Date.now() - startTime;
                        resolve({
                            success: false,
                            message: `Command timed out after ${timeout}ms: ${command}`,
                            error: 'TIMEOUT',
                            data: {
                                stdout: stdout.trim(),
                                stderr: stderr.trim(),
                                exitCode: -1,
                                command,
                                workingDirectory: cwd,
                                duration,
                                pid: childProcess.pid,
                            },
                        });
                    }, timeout);
                }
                // For detached processes, resolve immediately
                if (detached) {
                    isResolved = true;
                    resolve({
                        success: true,
                        message: `Detached command started: ${command}`,
                        data: {
                            stdout: '',
                            stderr: '',
                            exitCode: 0,
                            command,
                            workingDirectory: cwd,
                            duration: Date.now() - startTime,
                            pid: childProcess.pid,
                        },
                    });
                }
            });
        }
        catch (error) {
            logger_1.logger.error(`Failed to execute command: ${command}`, error, 'ShellOperations', context.sessionId);
            return {
                success: false,
                message: `Failed to execute command: ${command}`,
                error: error.message,
            };
        }
    }
    async executeScript(script, context, options = {}) {
        try {
            const { interpreter = 'bash' } = options;
            // Create a temporary script file
            const fs = await Promise.resolve().then(() => __importStar(require('fs-extra')));
            const path = await Promise.resolve().then(() => __importStar(require('path')));
            const { nanoid } = await Promise.resolve().then(() => __importStar(require('nanoid')));
            const tempDir = path.join(context.workingDirectory, '.tmp');
            await fs.ensureDir(tempDir);
            const scriptId = nanoid();
            const scriptPath = path.join(tempDir, `script_${scriptId}.sh`);
            await fs.writeFile(scriptPath, script, 'utf-8');
            await fs.chmod(scriptPath, '755');
            logger_1.logger.info(`Executing script`, {
                interpreter,
                scriptPath,
                scriptLength: script.length
            }, 'ShellOperations', context.sessionId);
            const result = await this.executeCommand(`${interpreter} "${scriptPath}"`, context, options);
            // Clean up temporary file
            try {
                await fs.remove(scriptPath);
            }
            catch (error) {
                logger_1.logger.warn(`Failed to clean up script file: ${scriptPath}`, error, 'ShellOperations', context.sessionId);
            }
            return result;
        }
        catch (error) {
            logger_1.logger.error(`Failed to execute script`, error, 'ShellOperations', context.sessionId);
            return {
                success: false,
                message: `Failed to execute script`,
                error: error.message,
            };
        }
    }
    killProcess(pid) {
        try {
            const process = this.runningProcesses.get(pid);
            if (process) {
                process.kill('SIGTERM');
                this.runningProcesses.delete(pid);
                logger_1.logger.info(`Process killed`, { pid }, 'ShellOperations');
                return true;
            }
            return false;
        }
        catch (error) {
            logger_1.logger.error(`Failed to kill process`, { pid, error }, 'ShellOperations');
            return false;
        }
    }
    killAllProcesses() {
        let killedCount = 0;
        for (const [pid, process] of this.runningProcesses) {
            try {
                process.kill('SIGTERM');
                killedCount++;
            }
            catch (error) {
                logger_1.logger.warn(`Failed to kill process ${pid}`, error, 'ShellOperations');
            }
        }
        this.runningProcesses.clear();
        logger_1.logger.info(`Killed ${killedCount} processes`, undefined, 'ShellOperations');
        return killedCount;
    }
    getRunningProcesses() {
        return Array.from(this.runningProcesses.entries()).map(([pid, process]) => ({
            pid,
            command: process.spawnargs?.join(' '),
        }));
    }
    validateCommand(command, _context) {
        const cliConfig = config_1.config.getConfig();
        if (!cliConfig.tools.allowShellExecution) {
            throw new Error('Shell execution is disabled in configuration');
        }
        // Basic command validation
        if (!command || command.trim().length === 0) {
            throw new Error('Command cannot be empty');
        }
        // Check for dangerous commands (basic protection)
        const dangerousCommands = [
            'rm -rf /',
            'format',
            'del /f /s /q',
            'shutdown',
            'reboot',
            'halt',
            'poweroff',
        ];
        const lowerCommand = command.toLowerCase();
        for (const dangerous of dangerousCommands) {
            if (lowerCommand.includes(dangerous)) {
                throw new Error(`Dangerous command detected: ${command}`);
            }
        }
    }
    parseCommand(command) {
        // Simple command parsing - you might want to use a more sophisticated parser
        const args = [];
        let current = '';
        let inQuotes = false;
        let quoteChar = '';
        for (let i = 0; i < command.length; i++) {
            const char = command[i];
            if ((char === '"' || char === "'") && !inQuotes) {
                inQuotes = true;
                quoteChar = char;
            }
            else if (char === quoteChar && inQuotes) {
                inQuotes = false;
                quoteChar = '';
            }
            else if (char === ' ' && !inQuotes) {
                if (current.trim()) {
                    args.push(current.trim());
                    current = '';
                }
            }
            else {
                current += char;
            }
        }
        if (current.trim()) {
            args.push(current.trim());
        }
        return args;
    }
}
exports.ShellOperations = ShellOperations;
exports.shellOperations = ShellOperations.getInstance();
//# sourceMappingURL=shell-ops.js.map