import { z } from 'zod';
import { Tool } from '@/types';
import { fileOperations } from '@/operations/file-ops';
import { shellOperations } from '@/operations/shell-ops';
import { logger } from '@/utils/logger';

// File operation tools
export const readFileTool: Tool = {
  name: 'read_file',
  description: 'Read the contents of a file',
  parameters: z.object({
    filePath: z.string().describe('Path to the file to read'),
  }),
  execute: async (params, context) => {
    return await fileOperations.readFile(params.filePath, context);
  },
};

export const writeFileTool: Tool = {
  name: 'write_file',
  description: 'Write content to a file',
  parameters: z.object({
    filePath: z.string().describe('Path to the file to write'),
    content: z.string().describe('Content to write to the file'),
    overwrite: z.boolean().optional().describe('Whether to overwrite existing file'),
    createDirs: z.boolean().optional().describe('Whether to create parent directories'),
  }),
  execute: async (params, context) => {
    return await fileOperations.writeFile(
      params.filePath,
      params.content,
      context,
      { overwrite: params.overwrite, createDirs: params.createDirs }
    );
  },
};

export const deleteFileTool: Tool = {
  name: 'delete_file',
  description: 'Delete a file or directory',
  parameters: z.object({
    filePath: z.string().describe('Path to the file or directory to delete'),
  }),
  execute: async (params, context) => {
    return await fileOperations.deleteFile(params.filePath, context);
  },
};

export const copyFileTool: Tool = {
  name: 'copy_file',
  description: 'Copy a file from source to destination',
  parameters: z.object({
    sourcePath: z.string().describe('Source file path'),
    destPath: z.string().describe('Destination file path'),
    overwrite: z.boolean().optional().describe('Whether to overwrite existing file'),
  }),
  execute: async (params, context) => {
    return await fileOperations.copyFile(
      params.sourcePath,
      params.destPath,
      context,
      { overwrite: params.overwrite }
    );
  },
};

export const moveFileTool: Tool = {
  name: 'move_file',
  description: 'Move a file from source to destination',
  parameters: z.object({
    sourcePath: z.string().describe('Source file path'),
    destPath: z.string().describe('Destination file path'),
  }),
  execute: async (params, context) => {
    return await fileOperations.moveFile(params.sourcePath, params.destPath, context);
  },
};

export const createDirectoryTool: Tool = {
  name: 'create_directory',
  description: 'Create a new directory',
  parameters: z.object({
    dirPath: z.string().describe('Path of the directory to create'),
  }),
  execute: async (params, context) => {
    return await fileOperations.createDirectory(params.dirPath, context);
  },
};

export const listDirectoryTool: Tool = {
  name: 'list_directory',
  description: 'List contents of a directory',
  parameters: z.object({
    dirPath: z.string().describe('Path of the directory to list'),
  }),
  execute: async (params, context) => {
    return await fileOperations.listDirectory(params.dirPath, context);
  },
};

export const searchFilesTool: Tool = {
  name: 'search_files',
  description: 'Search for files matching a pattern',
  parameters: z.object({
    pattern: z.string().describe('Glob pattern to search for files'),
    directory: z.string().optional().describe('Directory to search in (default: current)'),
    includeContent: z.boolean().optional().describe('Whether to include file content in results'),
    maxResults: z.number().optional().describe('Maximum number of results to return'),
    fileTypes: z.array(z.string()).optional().describe('File extensions to filter by'),
  }),
  execute: async (params, context) => {
    return await fileOperations.searchFiles(params.pattern, context, {
      directory: params.directory,
      includeContent: params.includeContent,
      maxResults: params.maxResults,
      fileTypes: params.fileTypes,
    });
  },
};

export const grepFilesTool: Tool = {
  name: 'grep_files',
  description: 'Search for text content within files',
  parameters: z.object({
    searchText: z.string().describe('Text to search for'),
    directory: z.string().optional().describe('Directory to search in (default: current)'),
    filePattern: z.string().optional().describe('File pattern to search within'),
    caseSensitive: z.boolean().optional().describe('Whether search is case sensitive'),
    maxResults: z.number().optional().describe('Maximum number of results to return'),
    contextLines: z.number().optional().describe('Number of context lines to include'),
  }),
  execute: async (params, context) => {
    return await fileOperations.grepFiles(params.searchText, context, {
      directory: params.directory,
      filePattern: params.filePattern,
      caseSensitive: params.caseSensitive,
      maxResults: params.maxResults,
      contextLines: params.contextLines,
    });
  },
};

export const getFileInfoTool: Tool = {
  name: 'get_file_info',
  description: 'Get detailed information about a file or directory',
  parameters: z.object({
    filePath: z.string().describe('Path to the file or directory'),
  }),
  execute: async (params, context) => {
    return await fileOperations.getFileInfo(params.filePath, context);
  },
};

export const setPermissionsTool: Tool = {
  name: 'set_permissions',
  description: 'Set file or directory permissions',
  parameters: z.object({
    filePath: z.string().describe('Path to the file or directory'),
    permissions: z.union([z.string(), z.number()]).describe('Permissions to set (e.g., "755" or 0o755)'),
  }),
  execute: async (params, context) => {
    return await fileOperations.setPermissions(params.filePath, params.permissions, context);
  },
};

// Shell operation tools
export const executeCommandTool: Tool = {
  name: 'execute_command',
  description: 'Execute a shell command',
  parameters: z.object({
    command: z.string().describe('Command to execute'),
    timeout: z.number().optional().describe('Timeout in milliseconds (default: 30000)'),
    cwd: z.string().optional().describe('Working directory for the command'),
    env: z.record(z.string()).optional().describe('Environment variables'),
    detached: z.boolean().optional().describe('Whether to run command in detached mode'),
  }),
  execute: async (params, context) => {
    return await shellOperations.executeCommand(params.command, context, {
      timeout: params.timeout,
      cwd: params.cwd,
      env: params.env,
      detached: params.detached,
    });
  },
};

export const executeScriptTool: Tool = {
  name: 'execute_script',
  description: 'Execute a script with specified interpreter',
  parameters: z.object({
    script: z.string().describe('Script content to execute'),
    interpreter: z.string().optional().describe('Script interpreter (default: bash)'),
    timeout: z.number().optional().describe('Timeout in milliseconds'),
    cwd: z.string().optional().describe('Working directory for the script'),
    env: z.record(z.string()).optional().describe('Environment variables'),
  }),
  execute: async (params, context) => {
    return await shellOperations.executeScript(params.script, context, {
      interpreter: params.interpreter,
      timeout: params.timeout,
      cwd: params.cwd,
      env: params.env,
    });
  },
};

export const killProcessTool: Tool = {
  name: 'kill_process',
  description: 'Kill a running process by PID',
  parameters: z.object({
    pid: z.number().describe('Process ID to kill'),
  }),
  execute: async (params, _context) => {
    const success = shellOperations.killProcess(params.pid);
    return {
      success,
      message: success 
        ? `Process ${params.pid} killed successfully`
        : `Failed to kill process ${params.pid} or process not found`,
      data: { pid: params.pid, killed: success },
    };
  },
};

export const listProcessesTool: Tool = {
  name: 'list_processes',
  description: 'List currently running processes started by the agent',
  parameters: z.object({}),
  execute: async (_params, _context) => {
    const processes = shellOperations.getRunningProcesses();
    return {
      success: true,
      message: `Found ${processes.length} running processes`,
      data: { processes, count: processes.length },
    };
  },
};

// Context and session tools
export const getProjectContextTool: Tool = {
  name: 'get_project_context',
  description: 'Get current project context and structure',
  parameters: z.object({}),
  execute: async (_params, context) => {
    return {
      success: true,
      message: 'Project context retrieved',
      data: {
        workingDirectory: context.workingDirectory,
        projectContext: context.projectContext,
        environment: context.environment,
      },
    };
  },
};

export const getSessionInfoTool: Tool = {
  name: 'get_session_info',
  description: 'Get current session information',
  parameters: z.object({}),
  execute: async (_params, context) => {
    return {
      success: true,
      message: 'Session information retrieved',
      data: {
        sessionId: context.sessionId,
        workingDirectory: context.workingDirectory,
        environment: Object.keys(context.environment).length,
      },
    };
  },
};

// Tool registry
export const allTools: Tool[] = [
  // File operations
  readFileTool,
  writeFileTool,
  deleteFileTool,
  copyFileTool,
  moveFileTool,
  createDirectoryTool,
  listDirectoryTool,
  searchFilesTool,
  grepFilesTool,
  getFileInfoTool,
  setPermissionsTool,
  
  // Shell operations
  executeCommandTool,
  executeScriptTool,
  killProcessTool,
  listProcessesTool,
  
  // Context operations
  getProjectContextTool,
  getSessionInfoTool,
];

export class ToolRegistry {
  private tools: Map<string, Tool> = new Map();

  constructor() {
    this.registerTools(allTools);
  }

  public registerTool(tool: Tool): void {
    this.tools.set(tool.name, tool);
    logger.debug(`Tool registered: ${tool.name}`, undefined, 'ToolRegistry');
  }

  public registerTools(tools: Tool[]): void {
    tools.forEach(tool => this.registerTool(tool));
  }

  public getTool(name: string): Tool | undefined {
    return this.tools.get(name);
  }

  public getAllTools(): Tool[] {
    return Array.from(this.tools.values());
  }

  public getToolNames(): string[] {
    return Array.from(this.tools.keys());
  }

  public hasTools(name: string): boolean {
    return this.tools.has(name);
  }

  public getToolsByCategory(category: string): Tool[] {
    return this.getAllTools().filter(tool => 
      tool.name.startsWith(category) || tool.description.toLowerCase().includes(category)
    );
  }
}

export const toolRegistry = new ToolRegistry();
