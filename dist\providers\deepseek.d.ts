import { <PERSON><PERSON><PERSON><PERSON>, AgentMessage, AgentConfig, Tool, Tool<PERSON>all } from '@/types';
export declare class DeepseekProvider implements LLMProvider {
    readonly name = "deepseek";
    private client;
    constructor();
    validateConfig(config: AgentConfig): boolean;
    sendMessage(messages: AgentMessage[], config: AgentConfig): Promise<string>;
    sendToolMessage(messages: AgentMessage[], tools: Tool[], config: AgentConfig): Promise<{
        message: string;
        toolCalls?: ToolCall[];
    }>;
    private formatMessages;
    private formatTools;
    private zodToJsonSchema;
}
//# sourceMappingURL=deepseek.d.ts.map